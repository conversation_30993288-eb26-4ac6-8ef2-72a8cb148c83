# app/api/endpoints/websocket.py
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from app.services.interview_service import InterviewService
from app.services.websocket.manager import get_websocket_handler
from app.services.websocketv2.video_enhanced_handler import VideoEnhancedWebSocketHandler
from app.services.azure.azure_speech_service import AzureSpeechService
from app.services.websocket.webrtc_manager import WebRTCManager
from app.services.websocket.video_manager import VideoManager
from app.services.websocket.message_dispatcher import (
    message_dispatcher, message_validator, rate_limiter, MessageType
)
from app.services.websocket.config import config
import json
import logging
import base64
import asyncio
from datetime import datetime

router = APIRouter()
logger = logging.getLogger(__name__)

# Global managers
webrtc_manager = WebRTCManager()
video_manager = VideoManager()

def get_interview_service() -> InterviewService:
    """Dependency injection cho InterviewService"""
    return InterviewService()

def get_video_websocket_handler() -> VideoEnhancedWebSocketHandler:
    """Get video-enhanced WebSocket handler"""
    return VideoEnhancedWebSocketHandler()

def get_azure_speech_service() -> AzureSpeechService:
    """Get Azure Speech Service"""
    return AzureSpeechService()


def _register_message_handlers(handler, interview_service, azure_speech):
    """Register message handlers with the dispatcher"""

    # Connection request handler
    async def handle_connection_request(websocket, session_id, message_data, context):
        await handler.utils.send_message(
            session_id,
            {
                "message": "Hẹ hẹ",
                "session_id": session_id,
                "status": "connected"
            },
            message_type="question_ready"
        )
        return True

    # TTS handler
    async def handle_tts_request(websocket, session_id, message_data, context):
        return await handle_tts_request_impl(websocket, message_data, azure_speech)

    # Pong handler
    async def handle_pong(websocket, session_id, message_data, context):
        logger.debug(f"Received pong from {session_id}")
        return True

    # WebRTC handlers
    async def handle_webrtc_offer(websocket, session_id, message_data, context):
        if not message_validator.validate_webrtc_offer(message_data):
            await _send_error(websocket, "Invalid WebRTC offer")
            return False

        return await webrtc_manager.handle_offer(
            session_id, message_data.get("offer"), websocket
        )

    async def handle_webrtc_candidate(websocket, session_id, message_data, context):
        if not message_validator.validate_webrtc_candidate(message_data):
            await _send_error(websocket, "Invalid WebRTC candidate")
            return False

        return await webrtc_manager.handle_ice_candidate(
            session_id, message_data.get("candidate")
        )

    async def handle_webrtc_ready(websocket, session_id, message_data, context):
        logger.info(f"WebRTC ready signal from {session_id}")
        await _send_ack(websocket, session_id, "webrtc_ready")
        return True

    # Video handlers
    async def handle_video_message(websocket, session_id, message_data, context):
        return await handler.handle_video_message(session_id, message_data)

    # Default candidate message handler
    async def handle_candidate_message(websocket, session_id, message_data, context):
        return await interview_service.communicate(session_id, message_data)

    # Register all handlers
    message_dispatcher.register_handler(
        MessageType.CONNECTION_REQUEST, handle_connection_request,
        "Handle connection request"
    )
    message_dispatcher.register_handler(
        MessageType.GENERATE_SPEECH, handle_tts_request,
        "Handle text-to-speech request"
    )
    message_dispatcher.register_handler(
        MessageType.PONG, handle_pong,
        "Handle pong message"
    )
    message_dispatcher.register_handler(
        MessageType.WEBRTC_OFFER, handle_webrtc_offer,
        "Handle WebRTC offer"
    )
    message_dispatcher.register_handler(
        MessageType.WEBRTC_CANDIDATE, handle_webrtc_candidate,
        "Handle WebRTC ICE candidate"
    )
    message_dispatcher.register_handler(
        MessageType.WEBRTC_READY, handle_webrtc_ready,
        "Handle WebRTC ready signal"
    )

    # Register video message handlers
    for msg_type in [MessageType.START_VIDEO_RECORDING, MessageType.VIDEO_CHUNK,
                     MessageType.END_VIDEO_RECORDING, MessageType.ANSWER_COMPLETED]:
        message_dispatcher.register_handler(
            msg_type, handle_video_message,
            f"Handle {msg_type.value}"
        )

    message_dispatcher.register_handler(
        MessageType.CANDIDATE_MESSAGE, handle_candidate_message,
        "Handle candidate message"
    )


async def _send_error(websocket: WebSocket, error_message: str):
    """Send error message to client"""
    try:
        error_response = {
            "type": "error",
            "error": error_message,
            "timestamp": datetime.utcnow().isoformat()
        }
        await websocket.send_text(json.dumps(error_response))
    except Exception as e:
        logger.error(f"Failed to send error message: {e}")


async def _send_ack(websocket: WebSocket, session_id: str, original_type: str):
    """Send acknowledgment message"""
    try:
        ack_response = {
            "type": "webrtc_ack",
            "sessionId": session_id,
            "originalType": original_type,
            "timestamp": datetime.utcnow().isoformat()
        }
        await websocket.send_text(json.dumps(ack_response))
    except Exception as e:
        logger.error(f"Failed to send ack message: {e}")


async def _cleanup_session(session_id: str, handler, keep_alive_task: asyncio.Task):
    """Clean up session resources"""
    try:
        # Cancel keep-alive task
        if keep_alive_task and not keep_alive_task.done():
            keep_alive_task.cancel()
            try:
                await asyncio.wait_for(keep_alive_task, timeout=5.0)
            except (asyncio.TimeoutError, asyncio.CancelledError):
                pass

        # Stop video processing
        video_manager.stop_recording(session_id)

        # Close WebRTC connection
        await webrtc_manager.close_connection(session_id)

        # Disconnect WebSocket handler
        await handler.disconnect(session_id)

        # Clean up rate limiting
        rate_limiter.cleanup_session(session_id)

        logger.info(f"Session cleanup completed for {session_id}")

    except Exception as e:
        logger.error(f"Error during session cleanup for {session_id}: {e}")


async def handle_video_track(track, session_id: str):
    """Handle incoming video track from WebRTC"""
    try:
        logger.info(f"Starting video processing for session {session_id}")

        # Start recording
        if not await video_manager.start_recording(session_id):
            logger.error(f"Failed to start video recording for {session_id}")
            return

        # Process frames
        while webrtc_manager.is_processing_video(session_id):
            try:
                frame = await asyncio.wait_for(track.recv(), timeout=config.websocket.video_frame_timeout)
                if frame is None:
                    logger.info(f"Received None frame for session {session_id}, ending video processing")
                    break

                success = await video_manager.process_frame(session_id, frame)
                if not success:
                    logger.warning(f"Failed to process frame for session {session_id}")
                    break

            except asyncio.TimeoutError:
                logger.debug(f"Timeout waiting for frame from session {session_id}")
                continue
            except Exception as e:
                logger.error(f"Error processing video frame for session {session_id}: {e}")
                if "MediaStreamError" in str(e) or "connection" in str(e).lower():
                    logger.info(f"Media stream ended for session {session_id}")
                    break
                continue

        # Stop recording and get final info
        recording_info = await video_manager.stop_recording(session_id)
        if recording_info:
            logger.info(f"Video processing completed for session {session_id}: {recording_info}")

    except Exception as e:
        logger.error(f"Video track handling error for session {session_id}: {e}")
    finally:
        webrtc_manager.stop_video_processing(session_id)


async def handle_tts_request_impl(websocket: WebSocket, message: dict, azure_speech: AzureSpeechService) -> bool:
    """Handle text-to-speech requests"""
    try:
        # Validate TTS request
        if not message_validator.validate_tts_request(message):
            await _send_error(websocket, "Invalid TTS request")
            return False

        text = message.get("text", "")
        voice = message.get("voice", "vi-VN-HoaiMyNeural")

        # Generate speech
        audio_data = await azure_speech.text_to_speech(text, voice)
        if not audio_data:
            raise Exception("No audio data generated")

        audio_base64 = base64.b64encode(audio_data).decode('utf-8')

        # Send response
        response = {
            "type": "tts_response",
            "text": text,
            "voice": voice,
            "audio": audio_base64,
            "timestamp": datetime.utcnow().isoformat()
        }

        await websocket.send_text(json.dumps(response))
        return True

    except Exception as e:
        logger.error(f"TTS generation failed: {e}")
        error_response = {
            "type": "tts_error",
            "error": f"Speech generation failed: {str(e)}",
            "timestamp": datetime.utcnow().isoformat()
        }
        await websocket.send_text(json.dumps(error_response))
        return False


@router.websocket("/ws/interview/{session_id}")
async def websocket_interview_endpoint(
    websocket: WebSocket,
    session_id: str,
    interview_service: InterviewService = Depends(get_interview_service),
    azure_speech: AzureSpeechService = Depends(get_azure_speech_service)
):
    """Refactored WebSocket endpoint with improved architecture"""

    # Initialize handlers and managers
    handler = get_video_websocket_handler()

    # Register message handlers
    _register_message_handlers(handler, interview_service, azure_speech)

    # Establish connection
    connection_success = await handler.connect(websocket, session_id)
    if not connection_success:
        logger.error(f"Failed to establish connection for {session_id}")
        return

    # Start keep-alive task
    keep_alive_task = asyncio.create_task(keep_alive_loop(websocket, session_id))

    # Initialize WebRTC connection
    await webrtc_manager.create_connection(
        session_id,
        on_track_callback=handle_video_track
    )

    last_activity = datetime.utcnow()

    try:
        while True:
            try:
                # Receive message with timeout
                data = await asyncio.wait_for(
                    websocket.receive_text(),
                    timeout=config.websocket.connection_timeout
                )
                last_activity = datetime.utcnow()

                # Validate and parse message
                message = message_validator.validate_json(data)
                if not message:
                    await _send_error(websocket, "Invalid JSON format")
                    continue

                if not message_validator.validate_message_structure(message):
                    await _send_error(websocket, "Invalid message structure")
                    continue

                # Check rate limiting
                if not await rate_limiter.check_rate_limit(session_id):
                    await _send_error(websocket, "Rate limit exceeded. Please slow down.")
                    continue

                # Dispatch message
                context = {
                    "handler": handler,
                    "interview_service": interview_service,
                    "azure_speech": azure_speech,
                    "last_activity": last_activity
                }

                success = await message_dispatcher.dispatch(
                    websocket, session_id, message, context
                )

                if not success:
                    logger.warning(f"Failed to process message for {session_id}")

            except asyncio.TimeoutError:
                # Check for activity timeout
                if (datetime.utcnow() - last_activity).total_seconds() > config.websocket.activity_timeout:
                    logger.warning(f"Connection timeout for {session_id}")
                    break
                continue

            except WebSocketDisconnect:
                logger.info(f"Client {session_id} disconnected normally")
                break

            except Exception as e:
                logger.error(f"WebSocket receive error for {session_id}: {e}")
                break

    except Exception as e:
        logger.error(f"WebSocket error for {session_id}: {e}")
    finally:
        await _cleanup_session(session_id, handler, keep_alive_task)

async def keep_alive_loop(websocket: WebSocket, session_id: str):
    """Keep WebSocket connection alive with periodic pings"""
    try:
        while True:
            await asyncio.sleep(config.websocket.ping_interval)
            if websocket.client_state.value == 1:
                try:
                    await websocket.send_text(json.dumps({
                        "type": "ping",
                        "timestamp": datetime.utcnow().isoformat()
                    }))
                    logger.debug(f"Sent ping to {session_id}")
                except Exception as e:
                    logger.warning(f"Failed to send ping to {session_id}: {e}")
                    break
            else:
                logger.info(f"WebSocket not connected for {session_id}, stopping keep-alive")
                break
    except asyncio.CancelledError:
        logger.debug(f"Keep-alive task cancelled for {session_id}")
    except Exception as e:
        logger.error(f"Keep-alive error for {session_id}: {e}")

# Old functions removed - functionality moved to managers



