# app/services/websocket/video_manager.py
import asyncio
import cv2
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
from dataclasses import dataclass, field

from .config import config

logger = logging.getLogger(__name__)


@dataclass
class VideoRecording:
    """Represents an active video recording"""
    session_id: str
    video_path: Path
    video_writer: Optional[cv2.VideoWriter] = None
    frame_count: int = 0
    start_time: float = field(default_factory=time.time)
    last_frame_time: float = field(default_factory=time.time)
    consecutive_errors: int = 0
    is_active: bool = True
    
    def update_frame_time(self):
        """Update last frame timestamp"""
        self.last_frame_time = time.time()
    
    def increment_error_count(self):
        """Increment consecutive error count"""
        self.consecutive_errors += 1
    
    def reset_error_count(self):
        """Reset consecutive error count"""
        self.consecutive_errors = 0
    
    def get_duration(self) -> float:
        """Get recording duration in seconds"""
        return time.time() - self.start_time
    
    def is_expired(self) -> bool:
        """Check if recording has expired due to inactivity"""
        return (time.time() - self.last_frame_time) > config.websocket.video_frame_timeout
    
    def has_too_many_errors(self) -> bool:
        """Check if recording has too many consecutive errors"""
        return self.consecutive_errors >= config.websocket.max_consecutive_errors


class VideoManager:
    """Manages video recording and processing"""
    
    def __init__(self):
        self.recordings: Dict[str, VideoRecording] = {}
        self._cleanup_task: Optional[asyncio.Task] = None
        self._start_cleanup_task()
    
    def _start_cleanup_task(self):
        """Start periodic cleanup of expired recordings"""
        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
    
    async def _periodic_cleanup(self):
        """Periodically clean up expired recordings"""
        while True:
            try:
                await asyncio.sleep(30)  # Check every 30 seconds
                await self._cleanup_expired_recordings()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in video cleanup: {e}")
    
    async def _cleanup_expired_recordings(self):
        """Clean up expired recordings"""
        expired_sessions = []
        
        for session_id, recording in self.recordings.items():
            if recording.is_expired() or recording.has_too_many_errors():
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            logger.info(f"Cleaning up expired video recording: {session_id}")
            await self.stop_recording(session_id)
    
    def _get_video_storage_path(self, session_id: str) -> Optional[Path]:
        """Get video storage path for session"""
        storage_path = config.get_video_storage_path()
        if not storage_path:
            logger.error("No available video storage path")
            return None
        
        timestamp = int(time.time() * 1000)
        filename = f"interview_{session_id}_{timestamp}.avi"
        return storage_path / filename
    
    def _initialize_video_writer(
        self, 
        video_path: Path, 
        frame_width: int, 
        frame_height: int
    ) -> Optional[cv2.VideoWriter]:
        """Initialize video writer"""
        try:
            fourcc = cv2.VideoWriter_fourcc(*config.webrtc.video_codec)
            video_writer = cv2.VideoWriter(
                str(video_path),
                fourcc,
                config.webrtc.video_quality,
                (frame_width, frame_height)
            )
            
            if not video_writer.isOpened():
                raise Exception("Failed to open video writer")
            
            logger.info(f"Video writer initialized: {frame_width}x{frame_height}")
            return video_writer
            
        except Exception as e:
            logger.error(f"Failed to initialize video writer: {e}")
            return None
    
    async def start_recording(self, session_id: str) -> bool:
        """Start video recording for session"""
        try:
            if session_id in self.recordings:
                logger.warning(f"Recording already active for session {session_id}")
                return True
            
            video_path = self._get_video_storage_path(session_id)
            if not video_path:
                return False
            
            recording = VideoRecording(
                session_id=session_id,
                video_path=video_path
            )
            
            self.recordings[session_id] = recording
            logger.info(f"Started video recording for session {session_id}: {video_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start recording for {session_id}: {e}")
            return False
    
    async def process_frame(self, session_id: str, frame) -> bool:
        """Process a video frame"""
        try:
            recording = self.recordings.get(session_id)
            if not recording or not recording.is_active:
                logger.warning(f"No active recording for session {session_id}")
                return False
            
            # Check if recording has exceeded maximum duration
            if recording.get_duration() > config.websocket.max_video_duration:
                logger.info(f"Maximum recording time reached for session {session_id}")
                await self.stop_recording(session_id)
                return False
            
            # Convert frame to numpy array
            img = frame.to_ndarray(format="bgr24")
            if img is None or img.size == 0:
                logger.warning(f"Invalid frame data for session {session_id}")
                recording.increment_error_count()
                
                if recording.has_too_many_errors():
                    logger.error(f"Too many invalid frames for session {session_id}")
                    await self.stop_recording(session_id)
                    return False
                return True
            
            # Initialize video writer if needed
            if recording.video_writer is None:
                frame_height, frame_width = img.shape[:2]
                recording.video_writer = self._initialize_video_writer(
                    recording.video_path, frame_width, frame_height
                )
                
                if recording.video_writer is None:
                    logger.error(f"Failed to initialize video writer for {session_id}")
                    await self.stop_recording(session_id)
                    return False
            
            # Write frame
            if recording.video_writer and recording.video_writer.isOpened():
                recording.video_writer.write(img)
                recording.frame_count += 1
                recording.update_frame_time()
                recording.reset_error_count()
                
                # Log progress periodically
                if recording.frame_count % 30 == 0:
                    duration = recording.get_duration()
                    logger.info(
                        f"Processed {recording.frame_count} frames for session {session_id} "
                        f"({duration:.1f}s)"
                    )
                
                return True
            else:
                logger.error(f"Video writer not available for session {session_id}")
                await self.stop_recording(session_id)
                return False
                
        except Exception as e:
            logger.error(f"Error processing frame for session {session_id}: {e}")
            recording = self.recordings.get(session_id)
            if recording:
                recording.increment_error_count()
                if recording.has_too_many_errors():
                    await self.stop_recording(session_id)
            return False
    
    async def stop_recording(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Stop video recording and return recording info"""
        try:
            recording = self.recordings.get(session_id)
            if not recording:
                logger.warning(f"No recording found for session {session_id}")
                return None
            
            recording.is_active = False
            
            # Release video writer
            if recording.video_writer:
                try:
                    recording.video_writer.release()
                    logger.info(f"Video writer released for session {session_id}")
                except Exception as e:
                    logger.error(f"Error releasing video writer: {e}")
            
            # Calculate final stats
            duration = recording.get_duration()
            
            # Remove from active recordings
            del self.recordings[session_id]
            
            recording_info = {
                "session_id": session_id,
                "video_path": str(recording.video_path),
                "frame_count": recording.frame_count,
                "duration": duration,
                "video_url": f"/candidates/{recording.video_path.name}",
                "completed_at": datetime.utcnow().isoformat()
            }
            
            logger.info(
                f"Video recording completed for session {session_id}: "
                f"{recording.frame_count} frames, {duration:.1f}s"
            )
            
            return recording_info
            
        except Exception as e:
            logger.error(f"Error stopping recording for session {session_id}: {e}")
            # Clean up anyway
            self.recordings.pop(session_id, None)
            return None
    
    def is_recording(self, session_id: str) -> bool:
        """Check if session is currently recording"""
        recording = self.recordings.get(session_id)
        return recording is not None and recording.is_active
    
    def get_recording_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get information about current recording"""
        recording = self.recordings.get(session_id)
        if not recording:
            return None
        
        return {
            "session_id": session_id,
            "frame_count": recording.frame_count,
            "duration": recording.get_duration(),
            "is_active": recording.is_active,
            "consecutive_errors": recording.consecutive_errors,
            "last_frame_time": recording.last_frame_time,
            "video_path": str(recording.video_path)
        }
    
    async def cleanup_all(self):
        """Clean up all recordings"""
        session_ids = list(self.recordings.keys())
        for session_id in session_ids:
            await self.stop_recording(session_id)
        
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get video manager statistics"""
        return {
            "active_recordings": len(self.recordings),
            "recordings": {
                session_id: {
                    "frame_count": recording.frame_count,
                    "duration": recording.get_duration(),
                    "is_active": recording.is_active,
                    "consecutive_errors": recording.consecutive_errors
                }
                for session_id, recording in self.recordings.items()
            }
        }
