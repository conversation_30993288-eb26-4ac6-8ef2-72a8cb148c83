# app/services/websocket/config.py
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
import os
from pathlib import Path


@dataclass
class WebSocketConfig:
    """Configuration for WebSocket connections"""

    # Connection settings
    connection_timeout: float = 60.0
    activity_timeout: float = 120.0
    ping_interval: float = 30.0

    # Rate limiting
    rate_limit_window: int = 60
    max_requests_per_window: int = 30

    # Video processing
    max_video_duration: float = 600.0  # 10 minutes
    video_frame_timeout: float = 30.0
    max_consecutive_errors: int = 10
    video_fps: float = 30.0

    # WebRTC settings
    ice_servers: List[Dict[str, Any]] = field(default_factory=lambda: [
        {"urls": "stun:stun.l.google.com:19302"}
    ])

    # Video storage paths
    video_storage_paths: List[Path] = field(default_factory=lambda: [
        Path("/app/candidates"),
        Path("/tmp/candidates"),
        Path("./candidates"),
        Path("candidates")
    ])

    # Message types configuration
    webrtc_message_types: List[str] = field(default_factory=lambda: [
        "webrtc_offer",
        "webrtc_answer",
        "webrtc_candidate",
        "webrtc_ready"
    ])

    video_message_types: List[str] = field(default_factory=lambda: [
        "start_video_recording",
        "video_chunk",
        "end_video_recording",
        "answer_completed"
    ])

    # Cleanup settings
    cleanup_timeout: float = 60.0
    max_reconnect_attempts: int = 3
    reconnect_delay: float = 2.0


@dataclass
class WebRTCConfig:
    """Configuration specific to WebRTC functionality"""

    # ICE candidate parsing
    ice_candidate_timeout: float = 5.0
    max_ice_candidates: int = 50

    # Video codec settings
    video_codec: str = "XVID"
    video_quality: int = 30  # FPS

    # Connection states
    valid_connection_states: List[str] = field(default_factory=lambda: [
        "new", "connecting", "connected", "disconnected", "failed", "closed"
    ])

    # Signaling timeouts
    offer_timeout: float = 10.0
    answer_timeout: float = 10.0
    candidate_timeout: float = 5.0


class ConfigManager:
    """Centralized configuration manager"""

    def __init__(self):
        self.websocket = WebSocketConfig()
        self.webrtc = WebRTCConfig()
        self._load_from_environment()

    def _load_from_environment(self):
        """Load configuration from environment variables"""
        # WebSocket settings
        if timeout := os.getenv("WS_CONNECTION_TIMEOUT"):
            self.websocket.connection_timeout = float(timeout)

        if activity_timeout := os.getenv("WS_ACTIVITY_TIMEOUT"):
            self.websocket.activity_timeout = float(activity_timeout)

        if ping_interval := os.getenv("WS_PING_INTERVAL"):
            self.websocket.ping_interval = float(ping_interval)

        # Rate limiting
        if rate_window := os.getenv("WS_RATE_LIMIT_WINDOW"):
            self.websocket.rate_limit_window = int(rate_window)

        if max_requests := os.getenv("WS_MAX_REQUESTS_PER_WINDOW"):
            self.websocket.max_requests_per_window = int(max_requests)

        # Video settings
        if max_duration := os.getenv("VIDEO_MAX_DURATION"):
            self.websocket.max_video_duration = float(max_duration)

        if frame_timeout := os.getenv("VIDEO_FRAME_TIMEOUT"):
            self.websocket.video_frame_timeout = float(frame_timeout)

        if max_errors := os.getenv("VIDEO_MAX_CONSECUTIVE_ERRORS"):
            self.websocket.max_consecutive_errors = int(max_errors)

        # WebRTC settings
        if ice_servers := os.getenv("WEBRTC_ICE_SERVERS"):
            try:
                import json
                self.webrtc.ice_servers = json.loads(ice_servers)
            except (json.JSONDecodeError, ValueError):
                pass  # Keep default

    def get_video_storage_path(self) -> Optional[Path]:
        """Get the first available video storage path"""
        for path in self.websocket.video_storage_paths:
            try:
                path.mkdir(exist_ok=True)
                # Test write permissions
                test_file = path / "test_write.tmp"
                test_file.write_text("test")
                test_file.unlink()
                return path
            except (PermissionError, OSError):
                continue
        return None

    def is_webrtc_message(self, message_type: str) -> bool:
        """Check if message type is WebRTC related"""
        return message_type in self.websocket.webrtc_message_types

    def is_video_message(self, message_type: str) -> bool:
        """Check if message type is video related"""
        return message_type in self.websocket.video_message_types


# Global configuration instance
config = ConfigManager()


@dataclass
class VideoConfig:
    """Configuration for video processing"""
    
    # Directory settings
    candidates_dir_paths: List[str] = None
    video_codec: str = "XVID"
    
    # File naming
    filename_template: str = "interview_{session_id}_{timestamp}.avi"
    
    def __post_init__(self):
        if self.candidates_dir_paths is None:
            self.candidates_dir_paths = [
                "/app/candidates",
                "/tmp/candidates", 
                "./candidates",
                "candidates"
            ]


@dataclass
class MessageConfig:
    """Configuration for message handling"""
    
    # Message types
    webrtc_message_types: List[str] = None
    video_message_types: List[str] = None
    
    def __post_init__(self):
        if self.webrtc_message_types is None:
            self.webrtc_message_types = [
                "webrtc_offer", 
                "webrtc_answer", 
                "webrtc_candidate", 
                "webrtc_ready"
            ]
        
        if self.video_message_types is None:
            self.video_message_types = [
                "start_video_recording", 
                "video_chunk", 
                "end_video_recording", 
                "answer_completed"
            ]


class WebSocketConfigManager:
    """Centralized configuration manager"""
    
    def __init__(self):
        self.websocket = WebSocketConfig()
        self.video = VideoConfig()
        self.message = MessageConfig()
    
    @classmethod
    def from_env(cls) -> "WebSocketConfigManager":
        """Create configuration from environment variables"""
        config = cls()
        
        # Override with environment variables if present
        config.websocket.connection_timeout = float(
            os.getenv("WS_CONNECTION_TIMEOUT", config.websocket.connection_timeout)
        )
        config.websocket.activity_timeout = float(
            os.getenv("WS_ACTIVITY_TIMEOUT", config.websocket.activity_timeout)
        )
        config.websocket.ping_interval = float(
            os.getenv("WS_PING_INTERVAL", config.websocket.ping_interval)
        )
        
        return config


# Global configuration instance
config = WebSocketConfigManager.from_env()
