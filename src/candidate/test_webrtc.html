<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WebRTC Self-Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .video-container {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
    }
    video {
      width: 400px;
      height: 300px;
      background: #000;
      border-radius: 8px;
    }
    .controls {
      margin-bottom: 20px;
    }
    button {
      padding: 8px 16px;
      margin-right: 10px;
      cursor: pointer;
      background: #4A90E2;
      color: white;
      border: none;
      border-radius: 4px;
    }
    button:disabled {
      background: #ccc;
      cursor: not-allowed;
    }
    .logs {
      height: 200px;
      overflow-y: auto;
      border: 1px solid #ccc;
      padding: 10px;
      background: #f7f7f7;
    }
    .log-entry {
      margin-bottom: 5px;
      font-family: monospace;
    }
    .status {
      margin-bottom: 10px;
      padding: 10px;
      border-radius: 4px;
    }
    .status.connected {
      background-color: #DFF2BF;
      color: #4F8A10;
    }
    .status.disconnected {
      background-color: #FEEFB3;
      color: #9F6000;
    }
    .status.error {
      background-color: #FFBABA;
      color: #D8000C;
    }
  </style>
</head>
<body>
  <h1>WebRTC Self-Test</h1>
  
  <div id="status" class="status disconnected">
    Status: Not initialized
  </div>
  
  <div class="video-container">
    <div>
      <h3>Local Video</h3>
      <video id="localVideo" autoplay muted playsinline></video>
    </div>
    <div>
      <h3>Remote Video (Loopback)</h3>
      <video id="remoteVideo" autoplay playsinline></video>
    </div>
  </div>
  
  <div class="controls">
    <button id="startButton">Start</button>
    <button id="stopButton" disabled>Stop</button>
  </div>
  
  <h3>Connection Info:</h3>
  <pre id="connectionInfo">No connection</pre>
  
  <h3>Logs:</h3>
  <div id="logs" class="logs"></div>
  
  <script>
    class WebRTCClient {
      constructor() {
        this.elements = {
          localVideo: document.getElementById('localVideo'),
          remoteVideo: document.getElementById('remoteVideo'),
          startButton: document.getElementById('startButton'),
          stopButton: document.getElementById('stopButton'),
          statusDiv: document.getElementById('status'),
          logsDiv: document.getElementById('logs'),
          connectionInfoDiv: document.getElementById('connectionInfo')
        };
        
        this.state = {
          ws: null,
          peerConnection: null,
          localStream: null,
          remoteStream: null,
          candidateQueue: [],
          remoteDescSet: false,
          isActive: false,
          reconnectAttempts: 0
        };
        
        this.config = {
          sessionId: 'test-session',
          wsUrl: `ws://localhost:8080/ws/interview/test-session`,
          maxReconnectAttempts: 3,
          iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
        };
        
        this.intervals = new Set();
        this.setupEventListeners();
        this.updateStatus('Ready', 'disconnected');
      }
      
      log(message) {
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        logEntry.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
        this.elements.logsDiv.appendChild(logEntry);
        this.elements.logsDiv.scrollTop = this.elements.logsDiv.scrollHeight;
        console.log(message);
      }
      
      updateStatus(message, type) {
        this.elements.statusDiv.textContent = `Status: ${message}`;
        this.elements.statusDiv.className = `status ${type}`;
      }
      
      updateConnectionInfo() {
        if (!this.state.peerConnection) return;
        
        const pc = this.state.peerConnection;
        const info = {
          signaling: pc.signalingState,
          ice: pc.iceConnectionState,
          connection: pc.connectionState
        };
        
        this.elements.connectionInfoDiv.textContent = JSON.stringify(info, null, 2);
      }
      
      setupEventListeners() {
        this.elements.startButton.addEventListener('click', () => this.start());
        this.elements.stopButton.addEventListener('click', () => this.stop());
        
        // Simplified cleanup on page unload
        window.addEventListener('beforeunload', () => {
          if (this.state.isActive) {
            this.cleanup();
          }
        });
      }
      
      async connectWebSocket() {
        return new Promise((resolve, reject) => {
          try {
            this.state.ws = new WebSocket(this.config.wsUrl);
            
            this.state.ws.onopen = () => {
              this.log("WebSocket connected");
              this.state.reconnectAttempts = 0;
              this.state.ws.send(JSON.stringify({ 
                type: "webrtc_ready", 
                sessionId: this.config.sessionId 
              }));
              resolve();
            };
            
            this.state.ws.onmessage = (event) => this.handleWebSocketMessage(event);
            this.state.ws.onclose = (e) => this.handleWebSocketClose(e);
            this.state.ws.onerror = (e) => {
              this.log(`WebSocket error: ${e.message || 'Unknown error'}`);
              reject(new Error('WebSocket connection failed'));
            };
            
          } catch (error) {
            reject(error);
          }
        });
      }
      
      handleWebSocketMessage(event) {
        try {
          const msg = JSON.parse(event.data);
          
          switch (msg.type) {
            case "ping":
              this.state.ws.send(JSON.stringify({ type: "pong", timestamp: msg.timestamp }));
              break;
              
            case "webrtc_answer":
              this.handleAnswer(msg.answer);
              break;
              
            case "webrtc_candidate":
              this.handleCandidate(msg.candidate);
              break;
              
            case "webrtc_error":
              this.log(`Server error: ${msg.error}`);
              this.updateStatus(`Error: ${msg.error}`, 'error');
              break;
          }
        } catch (error) {
          this.log(`Message parse error: ${error.message}`);
        }
      }
      
      handleWebSocketClose(e) {
        this.log(`WebSocket closed: ${e.code}`);
        
        if (this.state.isActive && this.state.reconnectAttempts < this.config.maxReconnectAttempts) {
          this.state.reconnectAttempts++;
          this.log(`Reconnecting... (${this.state.reconnectAttempts}/${this.config.maxReconnectAttempts})`);
          
          setTimeout(() => {
            if (this.state.isActive) {
              this.connectWebSocket().catch(() => {
                this.updateStatus('Connection failed', 'error');
              });
            }
          }, 2000);
        } else if (this.state.isActive) {
          this.updateStatus('Connection lost', 'error');
        }
      }
      
      async handleAnswer(answer) {
        if (!this.state.peerConnection || this.state.peerConnection.signalingState === 'closed') return;
        
        try {
          await this.state.peerConnection.setRemoteDescription(new RTCSessionDescription(answer));
          this.state.remoteDescSet = true;
          this.log("Remote description set");
          
          // Process queued candidates
          for (const candidate of this.state.candidateQueue) {
            await this.state.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
          }
          this.state.candidateQueue = [];
          
        } catch (error) {
          this.log(`Error setting remote description: ${error.message}`);
        }
      }
      
      async handleCandidate(candidate) {
        const rtcCandidate = {
          candidate: candidate.candidate,
          sdpMid: candidate.sdpMid,
          sdpMLineIndex: candidate.sdpMLineIndex
        };
        
        if (this.state.remoteDescSet && this.state.peerConnection) {
          try {
            await this.state.peerConnection.addIceCandidate(new RTCIceCandidate(rtcCandidate));
          } catch (error) {
            this.log(`Error adding ICE candidate: ${error.message}`);
          }
        } else {
          this.state.candidateQueue.push(rtcCandidate);
        }
      }
      
      setupPeerConnection() {
        this.state.peerConnection = new RTCPeerConnection({ iceServers: this.config.iceServers });
        const pc = this.state.peerConnection;
        
        // Add tracks
        this.state.localStream.getTracks().forEach(track => {
          pc.addTrack(track, this.state.localStream);
        });
        
        // Setup remote stream
        this.state.remoteStream = new MediaStream();
        this.elements.remoteVideo.srcObject = this.state.remoteStream;
        
        pc.ontrack = (event) => {
          event.streams[0].getTracks().forEach(track => {
            this.state.remoteStream.addTrack(track);
          });
        };
        
        pc.onicecandidate = (event) => {
          if (event.candidate && this.state.ws?.readyState === WebSocket.OPEN) {
            this.state.ws.send(JSON.stringify({
              type: "webrtc_candidate",
              candidate: {
                candidate: event.candidate.candidate,
                sdpMid: event.candidate.sdpMid,
                sdpMLineIndex: event.candidate.sdpMLineIndex
              },
              sessionId: this.config.sessionId
            }));
          }
        };
        
        pc.oniceconnectionstatechange = () => {
          this.updateConnectionInfo();
          
          if (pc.iceConnectionState === 'connected') {
            this.updateStatus('Connected', 'connected');
            this.log('WebRTC connected');
          } else if (pc.iceConnectionState === 'failed' || pc.iceConnectionState === 'disconnected') {
            this.updateStatus('Connection failed', 'error');
          }
        };
        
        // Update connection info periodically
        const infoInterval = setInterval(() => this.updateConnectionInfo(), 2000);
        this.intervals.add(infoInterval);
      }
      
      async start() {
        if (this.state.isActive) return;
        
        try {
          this.state.isActive = true;
          this.updateStatus('Starting...', 'disconnected');
          this.elements.startButton.disabled = true;
          
          // Get media first
          this.state.localStream = await navigator.mediaDevices.getUserMedia({ 
            video: true, 
            audio: true 
          });
          this.elements.localVideo.srcObject = this.state.localStream;
          this.log('Media acquired');
          
          // Connect WebSocket
          await this.connectWebSocket();
          
          // Setup peer connection
          this.setupPeerConnection();
          
          // Create and send offer
          const offer = await this.state.peerConnection.createOffer();
          await this.state.peerConnection.setLocalDescription(offer);
          
          this.state.ws.send(JSON.stringify({
            type: "webrtc_offer",
            offer,
            sessionId: this.config.sessionId
          }));
          
          this.updateStatus('Connecting...', 'disconnected');
          this.elements.stopButton.disabled = false;
          this.log('Connection initiated');
          
        } catch (error) {
          this.log(`Start error: ${error.message}`);
          this.updateStatus(`Error: ${error.message}`, 'error');
          this.stop();
        }
      }
      
      stop() {
        this.state.isActive = false;
        this.cleanup();
        this.updateStatus('Stopped', 'disconnected');
        this.elements.startButton.disabled = false;
        this.elements.stopButton.disabled = true;
        this.log('Stopped');
      }
      
      cleanup() {
        // Clear intervals
        this.intervals.forEach(interval => clearInterval(interval));
        this.intervals.clear();
        
        // Stop media tracks
        if (this.state.localStream) {
          this.state.localStream.getTracks().forEach(track => track.stop());
          this.state.localStream = null;
        }
        
        // Close peer connection
        if (this.state.peerConnection) {
          this.state.peerConnection.close();
          this.state.peerConnection = null;
        }
        
        // Close WebSocket
        if (this.state.ws && this.state.ws.readyState === WebSocket.OPEN) {
          this.state.ws.close(1000, 'Client cleanup');
          this.state.ws = null;
        }
        
        // Reset video elements
        this.elements.localVideo.srcObject = null;
        this.elements.remoteVideo.srcObject = null;
        
        // Reset state
        this.state.remoteStream = null;
        this.state.candidateQueue = [];
        this.state.remoteDescSet = false;
        this.state.reconnectAttempts = 0;
        
        this.elements.connectionInfoDiv.textContent = 'No connection';
      }
    }
    
    // Initialize the client
    const client = new WebRTCClient();
  </script>
</body>
</html>