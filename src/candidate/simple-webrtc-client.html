<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Simple WebRTC Test Client</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background: #f5f5f5;
    }
    
    .container {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    h1 {
      text-align: center;
      color: #333;
      margin-bottom: 24px;
    }
    
    .video-container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-bottom: 24px;
    }
    
    .video-wrapper {
      position: relative;
    }
    
    .video-wrapper h3 {
      margin: 0 0 8px 0;
      color: #555;
      font-size: 14px;
    }
    
    video {
      width: 100%;
      height: 200px;
      background: #000;
      border-radius: 8px;
      object-fit: cover;
    }
    
    .controls {
      display: flex;
      gap: 12px;
      justify-content: center;
      margin-bottom: 24px;
    }
    
    button {
      padding: 12px 24px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      min-width: 100px;
    }
    
    button:hover:not(:disabled) {
      transform: translateY(-1px);
    }
    
    button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }
    
    .btn-primary {
      background: #4A90E2;
      color: white;
    }
    
    .btn-primary:hover:not(:disabled) {
      background: #357ABD;
    }
    
    .btn-danger {
      background: #dc3545;
      color: white;
    }
    
    .btn-danger:hover:not(:disabled) {
      background: #c82333;
    }
    
    .status {
      padding: 12px 16px;
      border-radius: 6px;
      margin-bottom: 16px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .status::before {
      content: '';
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: currentColor;
    }
    
    .status.connected {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .status.disconnected {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }
    
    .status.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    .info-section {
      background: #f8f9fa;
      border-radius: 6px;
      padding: 16px;
      margin-bottom: 16px;
    }
    
    .info-section h3 {
      margin: 0 0 12px 0;
      color: #495057;
      font-size: 16px;
    }
    
    .connection-info {
      font-family: 'Courier New', monospace;
      font-size: 12px;
      background: #fff;
      padding: 12px;
      border-radius: 4px;
      border: 1px solid #dee2e6;
      white-space: pre-wrap;
      max-height: 120px;
      overflow-y: auto;
    }
    
    .logs {
      background: #1e1e1e;
      color: #f8f9fa;
      border-radius: 6px;
      padding: 16px;
      height: 200px;
      overflow-y: auto;
      font-family: 'Courier New', monospace;
      font-size: 13px;
      line-height: 1.4;
    }
    
    .log-entry {
      margin-bottom: 4px;
      padding: 2px 0;
    }
    
    .log-entry:hover {
      background: rgba(255, 255, 255, 0.05);
    }
    
    .config-section {
      background: #f8f9fa;
      border-radius: 6px;
      padding: 16px;
      margin-bottom: 16px;
    }
    
    .config-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;
    }
    
    .config-item label {
      display: block;
      margin-bottom: 4px;
      font-weight: 500;
      color: #495057;
      font-size: 14px;
    }
    
    .config-item input {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ced4da;
      border-radius: 4px;
      font-size: 14px;
    }
    
    .config-item input:focus {
      outline: none;
      border-color: #4A90E2;
      box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
    }
    
    @media (max-width: 600px) {
      .video-container {
        grid-template-columns: 1fr;
      }
      
      .controls {
        flex-direction: column;
        align-items: center;
      }
      
      button {
        width: 100%;
        max-width: 200px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Simple WebRTC Test Client</h1>
    
    <!-- Configuration Section -->
    <div class="config-section">
      <h3>Configuration</h3>
      <div class="config-grid">
        <div class="config-item">
          <label for="sessionId">Session ID:</label>
          <input type="text" id="sessionId" value="test-session">
        </div>
        <div class="config-item">
          <label for="wsUrl">WebSocket URL:</label>
          <input type="text" id="wsUrl" value="ws://localhost:8080/ws/interview/">
        </div>
      </div>
    </div>
    
    <!-- Status Display -->
    <div id="status" class="status disconnected">
      Status: Ready to start
    </div>
    
    <!-- Video Streams -->
    <div class="video-container">
      <div class="video-wrapper">
        <h3>Local Video</h3>
        <video id="localVideo" autoplay muted playsinline></video>
      </div>
      <div class="video-wrapper">
        <h3>Remote Video (Loopback)</h3>
        <video id="remoteVideo" autoplay playsinline></video>
      </div>
    </div>
    
    <!-- Controls -->
    <div class="controls">
      <button id="startButton" class="btn-primary">Start Test</button>
      <button id="stopButton" class="btn-danger" disabled>Stop Test</button>
    </div>
    
    <!-- Connection Information -->
    <div class="info-section">
      <h3>Connection Information</h3>
      <div id="connectionInfo" class="connection-info">No connection</div>
    </div>
    
    <!-- Debug Logs -->
    <div class="info-section">
      <h3>Debug Logs</h3>
      <div id="logs" class="logs"></div>
    </div>
  </div>

  <script>
    class SimpleWebRTCClient {
      constructor() {
        // DOM elements
        this.elements = {
          localVideo: document.getElementById('localVideo'),
          remoteVideo: document.getElementById('remoteVideo'),
          startButton: document.getElementById('startButton'),
          stopButton: document.getElementById('stopButton'),
          statusDiv: document.getElementById('status'),
          logsDiv: document.getElementById('logs'),
          connectionInfoDiv: document.getElementById('connectionInfo'),
          sessionIdInput: document.getElementById('sessionId'),
          wsUrlInput: document.getElementById('wsUrl')
        };
        
        // Application state
        this.state = {
          ws: null,
          peerConnection: null,
          localStream: null,
          remoteStream: null,
          candidateQueue: [],
          remoteDescSet: false,
          isActive: false,
          reconnectAttempts: 0
        };
        
        // Configuration
        this.config = {
          sessionId: 'test-session',
          wsUrl: 'ws://localhost:8080/ws/interview/',
          maxReconnectAttempts: 3,
          reconnectDelay: 2000,
          iceServers: [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' }
          ]
        };
        
        // Intervals for cleanup
        this.intervals = new Set();
        
        this.initialize();
      }
      
      initialize() {
        this.setupEventListeners();
        this.log('Simple WebRTC Test Client initialized');
      }
      
      // Enhanced logging with timestamps and better formatting
      log(message, level = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        
        // Add color coding based on level
        const colors = {
          info: '#17a2b8',
          success: '#28a745',
          warning: '#ffc107',
          error: '#dc3545',
          debug: '#6c757d'
        };
        
        logEntry.innerHTML = `<span style="color: #6c757d">[${timestamp}]</span> <span style="color: ${colors[level] || colors.info}">[${level.toUpperCase()}]</span> ${message}`;
        
        this.elements.logsDiv.appendChild(logEntry);
        this.elements.logsDiv.scrollTop = this.elements.logsDiv.scrollHeight;
        
        // Console logging with appropriate method
        const consoleMethod = console[level] || console.log;
        consoleMethod(`[${level.toUpperCase()}] ${message}`);
      }
      
      // Enhanced status updates with better visual feedback
      updateStatus(message, type) {
        this.elements.statusDiv.textContent = `Status: ${message}`;
        this.elements.statusDiv.className = `status ${type}`;
        this.log(`Status: ${message}`, type === 'error' ? 'error' : 'info');
      }
      
      // Enhanced connection info display
      updateConnectionInfo() {
        if (!this.state.peerConnection) {
          this.elements.connectionInfoDiv.textContent = 'No connection';
          return;
        }
        
        const pc = this.state.peerConnection;
        const info = {
          signaling: pc.signalingState,
          ice: pc.iceConnectionState,
          connection: pc.connectionState,
          localTracks: this.state.localStream?.getTracks().length || 0,
          remoteTracks: this.state.remoteStream?.getTracks().length || 0,
          queuedCandidates: this.state.candidateQueue.length
        };
        
        this.elements.connectionInfoDiv.textContent = JSON.stringify(info, null, 2);
      }
      
      setupEventListeners() {
        this.elements.startButton.addEventListener('click', () => this.start());
        this.elements.stopButton.addEventListener('click', () => this.stop());
        
        // Configuration updates
        this.elements.sessionIdInput.addEventListener('change', (e) => {
          this.config.sessionId = e.target.value;
          this.log(`Session ID updated: ${this.config.sessionId}`);
        });
        
        this.elements.wsUrlInput.addEventListener('change', (e) => {
          this.config.wsUrl = e.target.value;
          this.log(`WebSocket URL updated: ${this.config.wsUrl}`);
        });
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
          if (this.state.isActive) {
            this.cleanup();
          }
        });
      }

      // Enhanced WebSocket connection with better error handling
      async connectWebSocket() {
        return new Promise((resolve, reject) => {
          try {
            this.log(`Connecting to WebSocket: ${this.config.wsUrl}`);
            this.state.ws = new WebSocket(this.config.wsUrl);

            // Set connection timeout
            const timeout = setTimeout(() => {
              if (this.state.ws.readyState === WebSocket.CONNECTING) {
                this.state.ws.close();
                reject(new Error('WebSocket connection timeout'));
              }
            }, 10000);

            this.state.ws.onopen = () => {
              clearTimeout(timeout);
              this.log("WebSocket connected successfully", 'success');
              this.state.reconnectAttempts = 0;

              // Send ready message
              this.state.ws.send(JSON.stringify({
                type: "webrtc_ready",
                sessionId: this.config.sessionId
              }));

              resolve();
            };

            this.state.ws.onmessage = (event) => this.handleWebSocketMessage(event);
            this.state.ws.onclose = (e) => this.handleWebSocketClose(e);
            this.state.ws.onerror = (e) => {
              clearTimeout(timeout);
              this.log(`WebSocket error: ${e.message || 'Connection failed'}`, 'error');
              reject(new Error('WebSocket connection failed'));
            };

          } catch (error) {
            this.log(`WebSocket connection error: ${error.message}`, 'error');
            reject(error);
          }
        });
      }

      handleWebSocketMessage(event) {
        try {
          const msg = JSON.parse(event.data);
          this.log(`Received message: ${msg.type}`, 'debug');

          switch (msg.type) {
            case "ping":
              this.state.ws.send(JSON.stringify({
                type: "pong",
                timestamp: msg.timestamp
              }));
              break;

            case "webrtc_answer":
              this.handleAnswer(msg.answer);
              break;

            case "webrtc_candidate":
              this.handleCandidate(msg.candidate);
              break;

            case "webrtc_error":
              this.log(`Server error: ${msg.error}`, 'error');
              this.updateStatus(`Server Error: ${msg.error}`, 'error');
              break;

            default:
              this.log(`Unknown message type: ${msg.type}`, 'warning');
          }
        } catch (error) {
          this.log(`Message parse error: ${error.message}`, 'error');
        }
      }

      handleWebSocketClose(e) {
        this.log(`WebSocket closed: Code ${e.code}, Reason: ${e.reason || 'Unknown'}`, 'warning');

        if (this.state.isActive && this.state.reconnectAttempts < this.config.maxReconnectAttempts) {
          this.state.reconnectAttempts++;
          this.log(`Attempting reconnection ${this.state.reconnectAttempts}/${this.config.maxReconnectAttempts}`, 'info');
          this.updateStatus(`Reconnecting... (${this.state.reconnectAttempts}/${this.config.maxReconnectAttempts})`, 'disconnected');

          setTimeout(() => {
            if (this.state.isActive) {
              this.connectWebSocket().catch((error) => {
                this.log(`Reconnection failed: ${error.message}`, 'error');
                this.updateStatus('Connection failed', 'error');
              });
            }
          }, this.config.reconnectDelay);
        } else if (this.state.isActive) {
          this.updateStatus('Connection lost - Max reconnection attempts reached', 'error');
        }
      }

      async handleAnswer(answer) {
        if (!this.state.peerConnection || this.state.peerConnection.signalingState === 'closed') {
          this.log('Cannot handle answer - no active peer connection', 'warning');
          return;
        }

        try {
          await this.state.peerConnection.setRemoteDescription(new RTCSessionDescription(answer));
          this.state.remoteDescSet = true;
          this.log("Remote description set successfully", 'success');

          // Process queued ICE candidates
          this.log(`Processing ${this.state.candidateQueue.length} queued ICE candidates`);
          for (const candidate of this.state.candidateQueue) {
            try {
              await this.state.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
            } catch (error) {
              this.log(`Error adding queued ICE candidate: ${error.message}`, 'warning');
            }
          }
          this.state.candidateQueue = [];

        } catch (error) {
          this.log(`Error setting remote description: ${error.message}`, 'error');
        }
      }

      async handleCandidate(candidate) {
        const rtcCandidate = {
          candidate: candidate.candidate,
          sdpMid: candidate.sdpMid,
          sdpMLineIndex: candidate.sdpMLineIndex
        };

        if (this.state.remoteDescSet && this.state.peerConnection) {
          try {
            await this.state.peerConnection.addIceCandidate(new RTCIceCandidate(rtcCandidate));
            this.log('ICE candidate added successfully', 'debug');
          } catch (error) {
            this.log(`Error adding ICE candidate: ${error.message}`, 'warning');
          }
        } else {
          this.state.candidateQueue.push(rtcCandidate);
          this.log(`ICE candidate queued (${this.state.candidateQueue.length} total)`, 'debug');
        }
      }

      setupPeerConnection() {
        this.log('Setting up peer connection...');
        this.state.peerConnection = new RTCPeerConnection({
          iceServers: this.config.iceServers
        });

        const pc = this.state.peerConnection;

        // Add local stream tracks
        this.state.localStream.getTracks().forEach(track => {
          pc.addTrack(track, this.state.localStream);
          this.log(`Added ${track.kind} track to peer connection`);
        });

        // Setup remote stream
        this.state.remoteStream = new MediaStream();
        this.elements.remoteVideo.srcObject = this.state.remoteStream;

        // Handle incoming tracks
        pc.ontrack = (event) => {
          this.log(`Received remote ${event.track.kind} track`, 'success');
          event.streams[0].getTracks().forEach(track => {
            this.state.remoteStream.addTrack(track);
          });
          this.updateConnectionInfo();
        };

        // Handle ICE candidates
        pc.onicecandidate = (event) => {
          if (event.candidate && this.state.ws?.readyState === WebSocket.OPEN) {
            this.state.ws.send(JSON.stringify({
              type: "webrtc_candidate",
              candidate: {
                candidate: event.candidate.candidate,
                sdpMid: event.candidate.sdpMid,
                sdpMLineIndex: event.candidate.sdpMLineIndex
              },
              sessionId: this.config.sessionId
            }));
            this.log('ICE candidate sent', 'debug');
          }
        };

        // Handle connection state changes
        pc.oniceconnectionstatechange = () => {
          this.log(`ICE connection state: ${pc.iceConnectionState}`);
          this.updateConnectionInfo();

          switch (pc.iceConnectionState) {
            case 'connected':
            case 'completed':
              this.updateStatus('Connected', 'connected');
              this.log('WebRTC connection established', 'success');
              break;
            case 'failed':
            case 'disconnected':
              this.updateStatus('Connection failed', 'error');
              this.log('WebRTC connection failed', 'error');
              break;
            case 'checking':
              this.updateStatus('Connecting...', 'disconnected');
              break;
          }
        };

        // Handle signaling state changes
        pc.onsignalingstatechange = () => {
          this.log(`Signaling state: ${pc.signalingState}`);
          this.updateConnectionInfo();
        };

        // Handle connection state changes (newer browsers)
        pc.onconnectionstatechange = () => {
          this.log(`Connection state: ${pc.connectionState}`);
          this.updateConnectionInfo();
        };

        // Update connection info periodically
        const infoInterval = setInterval(() => {
          if (this.state.peerConnection) {
            this.updateConnectionInfo();
          }
        }, 2000);
        this.intervals.add(infoInterval);

        this.log('Peer connection setup complete');
      }

      async start() {
        if (this.state.isActive) {
          this.log('Test already active', 'warning');
          return;
        }

        try {
          this.state.isActive = true;
          this.updateStatus('Starting...', 'disconnected');
          this.elements.startButton.disabled = true;

          // Update configuration from inputs
          this.config.sessionId = this.elements.sessionIdInput.value;
          this.config.wsUrl = this.elements.wsUrlInput.value;

          this.log('Starting WebRTC test...', 'info');

          // Get user media first
          this.log('Requesting user media...');
          this.state.localStream = await navigator.mediaDevices.getUserMedia({
            video: { width: 640, height: 480 },
            audio: true
          });

          this.elements.localVideo.srcObject = this.state.localStream;
          this.log('Local media acquired successfully', 'success');

          // Connect WebSocket
          await this.connectWebSocket();

          // Setup peer connection
          this.setupPeerConnection();

          // Create and send offer
          this.log('Creating WebRTC offer...');
          const offer = await this.state.peerConnection.createOffer();
          await this.state.peerConnection.setLocalDescription(offer);

          this.state.ws.send(JSON.stringify({
            type: "webrtc_offer",
            offer,
            sessionId: this.config.sessionId
          }));

          this.updateStatus('Waiting for answer...', 'disconnected');
          this.elements.stopButton.disabled = false;
          this.log('WebRTC offer sent - waiting for response', 'info');

        } catch (error) {
          this.log(`Start error: ${error.message}`, 'error');
          this.updateStatus(`Error: ${error.message}`, 'error');
          this.stop();
        }
      }

      stop() {
        this.log('Stopping WebRTC test...');
        this.state.isActive = false;
        this.cleanup();
        this.updateStatus('Stopped', 'disconnected');
        this.elements.startButton.disabled = false;
        this.elements.stopButton.disabled = true;
        this.log('WebRTC test stopped', 'info');
      }

      cleanup() {
        this.log('Cleaning up resources...');

        // Clear intervals
        this.intervals.forEach(interval => {
          clearInterval(interval);
        });
        this.intervals.clear();

        // Stop media tracks
        if (this.state.localStream) {
          this.state.localStream.getTracks().forEach(track => {
            track.stop();
            this.log(`Stopped ${track.kind} track`);
          });
          this.state.localStream = null;
        }

        // Close peer connection
        if (this.state.peerConnection) {
          this.state.peerConnection.close();
          this.state.peerConnection = null;
          this.log('Peer connection closed');
        }

        // Close WebSocket
        if (this.state.ws && this.state.ws.readyState === WebSocket.OPEN) {
          this.state.ws.close(1000, 'Client cleanup');
          this.state.ws = null;
          this.log('WebSocket connection closed');
        }

        // Reset video elements
        this.elements.localVideo.srcObject = null;
        this.elements.remoteVideo.srcObject = null;

        // Reset state
        this.state.remoteStream = null;
        this.state.candidateQueue = [];
        this.state.remoteDescSet = false;
        this.state.reconnectAttempts = 0;

        this.elements.connectionInfoDiv.textContent = 'No connection';
        this.log('Cleanup complete');
      }
    }

    // Initialize the client when page loads
    document.addEventListener('DOMContentLoaded', () => {
      const client = new SimpleWebRTCClient();
    });
    </script>
  </body>
</html>
